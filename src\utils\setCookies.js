import Cookies from "js-cookie";
import CryptoJS from "crypto-js";

const secretKey = import.meta.env.VITE_SECRET_KEY;

function encryptData(data) {
  if (!data) {
    console.error("Invalid data to encrypt.");
    return null;
  }

  if (!secretKey) {
    console.error("Secret key is not defined.");
    return null;
  }

  try {
    const stringData = typeof data === "string" ? data : JSON.stringify(data);
    return CryptoJS.AES.encrypt(stringData, secretKey).toString();
  } catch (error) {
    console.error("Error encrypting data:", error);
    return null;
  }
}

function decryptData(encryptedData) {
  if (!encryptedData) {
    console.error("Invalid encrypted data.");
    return null;
  }

  if (!secretKey) {
    console.error("Secret key is not defined.");
    return null;
  }

  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, secretKey);
    const decrypted = bytes.toString(CryptoJS.enc.Utf8);
    return decrypted;
  } catch (error) {
    console.error("Error decrypting data:", error);
    return null;
  }
}

export const setCookies = (data) => {
  const { accessToken, userId, userType } = data;

  try {
    // Encrypt data
    const encryptedAccessToken = encryptData(accessToken);
    const encryptedUserId = encryptData(userId);

    // Store encrypted data in cookies
    if (encryptedAccessToken) {
      Cookies.set("accessToken", encryptedAccessToken, {
        expires: 1, // Expires in 1 day
        secure: true, // Ensures cookies are transmitted over HTTPS
        sameSite: "Strict",
      });
    }

    if (encryptedUserId) {
      Cookies.set("userId", encryptedUserId, {
        expires: 1,
        secure: true,
        sameSite: "Strict",
      });
    }

    Cookies.set("userType", userType, {
      expires: 1,
      secure: true,
      sameSite: "Strict",
    });

    console.log("Cookies set successfully.");
  } catch (error) {
    console.error("Error setting cookies:", error);
  }
};

export { encryptData, decryptData };
