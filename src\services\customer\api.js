import { apiRequest } from "@/utils/apiRequest";
import { decryptData } from "@/utils/setCookies";
import Cookies from "js-cookie";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

const getAuthHeaders = () => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  return {
    Authorization: `Bearer ${accessToken}`,
    "Content-Type": "application/json",
  };
};

export const getBusinessCategory = async () => {
  return apiRequest({
    url: `${BASE_URL}/customer/business-category`,
    method: "GET",
    headers: getAuthHeaders(),
  });
};

// ✅ GET all specifications
export const getAllSpecifications = async () => {
  return apiRequest({
    url: `${BASE_URL}/customer/specifications`,
    method: "GET",
    headers: getAuthHeaders(),
  });
};

// ✅ GET by ID
export const getSpecificationById = async (id) => {
  return apiRequest({
    url: `${BASE_URL}/customer/specifications/${id}`,
    method: "GET",
    headers: getAuthHeaders(),
  });
};

// ✅ POST - create
export const postSpecifications = async (data) => {
  return apiRequest({
    url: `${BASE_URL}/customer/specifications`,
    method: "POST",
    headers: getAuthHeaders(),
    body: data,
  });
};

// ✅ PUT - update
export const updateSpecification = async ({ id, data }) => {
  return apiRequest({
    url: `${BASE_URL}/customer/specifications/${id}`,
    method: "PUT",
    headers: getAuthHeaders(),
    body: data,
  });
};

// ✅ DELETE
export const deleteSpecification = async (id) => {
  return apiRequest({
    url: `${BASE_URL}/customer/specifications/${id}`,
    method: "DELETE",
    headers: getAuthHeaders(),
  });
};


export const verifyCustomerKyc = async (data) => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  const response = await fetch(`${BASE_URL}/customer/verify-kyc`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: data,
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "KYC verification failed");
  }
  return response.json();
};