import { apiRequest } from "@/utils/apiRequest";
import { decryptData } from "@/utils/setCookies";
import Cookies from "js-cookie";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const getProvince = async () => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  const response = await apiRequest({
    url: `${BASE_URL}/provinces`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const getDistrictByProvince = async (provinceId) => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  const response = await apiRequest({
    url: `${BASE_URL}/districts-by-province/${provinceId}`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const getMunicipalityByDistrict = async (districtId) => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  const response = await apiRequest({
    url: `${BASE_URL}/municipalities-by-district/${districtId}`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const getFaqs = async () => {
  const response = await apiRequest({
    url: `${BASE_URL}/faq`,
    method: "GET",
  });
  console.log(response.data);
  return response.data;
};
