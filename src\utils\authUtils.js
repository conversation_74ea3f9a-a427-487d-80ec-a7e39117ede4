import Cookies from "js-cookie";
import { decryptData } from "./setCookies";

/**
 * Check if user is authenticated by verifying cookies and Redux state
 * @param {Object} reduxUser - User object from Redux state
 * @returns {Object} Authentication status and user info
 */
export const getAuthenticationStatus = (reduxUser = null) => {
  try {
    // Check cookies first (primary source of truth)
    const accessToken = Cookies.get("accessToken");
    const encryptedUserId = Cookies.get("userId");
    const userType = Cookies.get("userType");

    // If no cookies, user is not authenticated
    if (!accessToken || !encryptedUserId || !userType) {
      return {
        isAuthenticated: false,
        userType: null,
        userId: null,
        dashboardPath: null,
      };
    }

    // Decrypt userId
    const userId = decryptData(encryptedUserId);

    // If decryption fails, user is not authenticated
    if (!userId) {
      return {
        isAuthenticated: false,
        userType: null,
        userId: null,
        dashboardPath: null,
      };
    }

    // Double-check with Redux state if available
    if (
      reduxUser &&
      reduxUser.isLogin &&
      reduxUser.userId &&
      reduxUser.userType
    ) {
      // Ensure Redux state matches cookies
      if (reduxUser.userId === userId && reduxUser.userType === userType) {
        return {
          isAuthenticated: true,
          userType: reduxUser.userType,
          userId: reduxUser.userId,
          dashboardPath: getDashboardPath(reduxUser.userType, reduxUser.userId),
        };
      }
    }

    // If Redux state doesn't match or isn't available, rely on cookies
    return {
      isAuthenticated: true,
      userType,
      userId,
      dashboardPath: getDashboardPath(userType, userId),
    };
  } catch (error) {
    console.error("Error checking authentication status:", error);
    return {
      isAuthenticated: false,
      userType: null,
      userId: null,
      dashboardPath: null,
    };
  }
};

/**
 * Get the appropriate dashboard path based on user type and ID
 * @param {string} userType - Type of user (customer, vendor, admin)
 * @param {string} userId - User ID
 * @returns {string} Dashboard path
 */
export const getDashboardPath = (userType, userId) => {
  switch (userType) {
    case "user":
      return `/customer/${userId}/dashboard`;
    case "vendor":
      return `/vendor/${userId}/dashboard`;
    case "admin":
      return `/admin/${userId}/dashboard`;
    default:
      return "/";
  }
};

/**
 * Check if the current path is an authentication route
 * @param {string} pathname - Current pathname
 * @returns {boolean} True if it's an auth route
 */
export const isAuthRoute = (pathname) => {
  const authRoutes = [
    "/customer/signin",
    "/vendor/signin",
    "/admin/signin",
    "/customer/signup",
    "/vendor/signup",
    "/customer/forgot-password",
    "/vendor/forgot-password",
    "/admin/forgot-password",
    "/customer/reset-password",
    "/vendor/reset-password",
    "/admin/reset-password",
  ];

  return authRoutes.includes(pathname);
};

/**
 * Check if the current path is a signup route
 * @param {string} pathname - Current pathname
 * @returns {boolean} True if it's a signup route
 */
export const isSignupRoute = (pathname) => {
  const signupRoutes = ["/customer/signup", "/vendor/signup"];

  return signupRoutes.includes(pathname);
};

/**
 * Check if the current path is a password reset route
 * @param {string} pathname - Current pathname
 * @returns {boolean} True if it's a password reset route
 */
export const isPasswordResetRoute = (pathname) => {
  const resetRoutes = [
    "/customer/forgot-password",
    "/vendor/forgot-password",
    "/admin/forgot-password",
    "/customer/reset-password",
    "/vendor/reset-password",
    "/admin/reset-password",
  ];

  return resetRoutes.includes(pathname);
};

/**
 * Clear all authentication data
 */
export const clearAuthData = () => {
  // Remove cookies
  Cookies.remove("accessToken");
  Cookies.remove("userId");
  Cookies.remove("userType");
  Cookies.remove("name");
  Cookies.remove("email");

  // Clear localStorage
  localStorage.clear();
};
